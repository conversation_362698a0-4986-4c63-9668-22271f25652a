import requests
import pandas as pd
import os
from datetime import datetime, timedelta
import time
import logging
from typing import List, Dict, Optional
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import re


class CWAScraper:
    def __init__(self):
        self.base_url = "https://codis.cwa.gov.tw"
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
        )

        # Setup logging
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger(__name__)

        # Create output directory
        self.output_dir = "cwa_data"
        os.makedirs(self.output_dir, exist_ok=True)

        # Setup Chrome driver
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # Run in background
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")

            # Set download directory
            prefs = {
                "download.default_directory": os.path.abspath(self.output_dir),
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True,
            }
            chrome_options.add_experimental_option("prefs", prefs)

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            self.logger.info("Chrome WebDriver initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup Chrome driver: {e}")
            self.logger.info("Please make sure ChromeDriver is installed and in PATH")

    def __del__(self):
        """Cleanup driver on destruction"""
        if self.driver:
            self.driver.quit()

    def get_agricultural_stations(self) -> List[Dict]:
        """Get list of agricultural stations from the website"""
        try:
            self.logger.info("Getting agricultural stations list...")
            self.driver.get(f"{self.base_url}/StationData")

            # Wait for page to load
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Look for agricultural station checkboxes or filters
            agricultural_stations = []

            # Try to find station elements - this might need adjustment based on actual website structure
            try:
                # Look for agricultural station filter/checkbox
                agri_checkbox = self.driver.find_element(
                    By.XPATH, "//input[@type='checkbox' and contains(@value, '農業')]"
                )
                if not agri_checkbox.is_selected():
                    agri_checkbox.click()
                    time.sleep(2)

                # Get station list after filtering
                station_elements = self.driver.find_elements(
                    By.CSS_SELECTOR, ".station-item, .station-row, [data-station-id]"
                )

                for element in station_elements:
                    try:
                        station_id = element.get_attribute(
                            "data-station-id"
                        ) or element.get_attribute("value")
                        station_name = element.text or element.get_attribute("title")

                        if station_id and station_name and "農業" in station_name:
                            agricultural_stations.append(
                                {
                                    "id": station_id,
                                    "name": station_name,
                                    "type": "agricultural",
                                }
                            )
                    except Exception:
                        continue

            except NoSuchElementException:
                self.logger.warning(
                    "Could not find agricultural station filter, trying alternative method"
                )
                # Alternative: get all stations and filter by name
                all_stations = self.driver.find_elements(
                    By.CSS_SELECTOR, "[data-station], .station"
                )
                for station in all_stations:
                    station_text = station.text or station.get_attribute("title") or ""
                    if "農業" in station_text:
                        agricultural_stations.append(
                            {
                                "id": station.get_attribute("data-station-id")
                                or station.get_attribute("value"),
                                "name": station_text,
                                "type": "agricultural",
                            }
                        )

            self.logger.info(
                f"Found {len(agricultural_stations)} agricultural stations"
            )
            return agricultural_stations

        except Exception as e:
            self.logger.error(f"Error getting agricultural stations: {e}")
            return []

    def download_monthly_data_selenium(
        self, station_id: str, station_name: str, year: int, month: int
    ) -> bool:
        """Download monthly data using Selenium for a specific station"""
        try:
            self.logger.info(
                f"Downloading data for {station_name} ({station_id}) - {year}/{month:02d}"
            )

            # Navigate to station data page
            self.driver.get(f"{self.base_url}/StationData")

            # Wait for page to load
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Select agricultural stations filter if available
            try:
                agri_checkbox = self.driver.find_element(
                    By.XPATH, "//input[@type='checkbox' and contains(@value, '農業')]"
                )
                if not agri_checkbox.is_selected():
                    agri_checkbox.click()
                    time.sleep(1)
            except NoSuchElementException:
                pass

            # Try to select the specific station
            try:
                # Look for station selection dropdown or list
                station_selector = self.driver.find_element(
                    By.XPATH, f"//option[contains(text(), '{station_name}')]"
                )
                station_selector.click()
                time.sleep(1)
            except NoSuchElementException:
                # Try alternative selection method
                try:
                    station_input = self.driver.find_element(
                        By.XPATH, f"//input[@value='{station_id}']"
                    )
                    station_input.click()
                    time.sleep(1)
                except NoSuchElementException:
                    self.logger.warning(f"Could not select station {station_name}")
                    return False

            # Select monthly report (月報表)
            try:
                monthly_radio = self.driver.find_element(
                    By.XPATH, "//input[@type='radio' and contains(@value, '月報表')]"
                )
                monthly_radio.click()
                time.sleep(1)
            except NoSuchElementException:
                try:
                    monthly_option = self.driver.find_element(
                        By.XPATH, "//option[contains(text(), '月報表')]"
                    )
                    monthly_option.click()
                    time.sleep(1)
                except NoSuchElementException:
                    self.logger.warning("Could not find monthly report option")

            # Set year and month
            try:
                year_select = Select(self.driver.find_element(By.NAME, "year"))
                year_select.select_by_value(str(year))
                time.sleep(0.5)

                month_select = Select(self.driver.find_element(By.NAME, "month"))
                month_select.select_by_value(str(month))
                time.sleep(0.5)
            except NoSuchElementException:
                # Try alternative date input methods
                try:
                    year_input = self.driver.find_element(
                        By.XPATH, f"//input[@value='{year}' or @placeholder='年']"
                    )
                    year_input.clear()
                    year_input.send_keys(str(year))

                    month_input = self.driver.find_element(
                        By.XPATH, f"//input[@value='{month}' or @placeholder='月']"
                    )
                    month_input.clear()
                    month_input.send_keys(str(month))
                except NoSuchElementException:
                    self.logger.warning(f"Could not set date {year}/{month}")
                    return False

            # Click download or CSV button
            try:
                csv_button = self.driver.find_element(
                    By.XPATH,
                    "//button[contains(text(), 'CSV')] | //a[contains(text(), 'CSV')] | //input[@value='CSV']",
                )
                csv_button.click()
                time.sleep(3)  # Wait for download

                # Check if file was downloaded
                expected_filename = (
                    f"{station_id}_{station_name}_{year}_{month:02d}.csv"
                )
                expected_path = os.path.join(self.output_dir, expected_filename)

                # Look for downloaded files in output directory
                downloaded_files = [
                    f for f in os.listdir(self.output_dir) if f.endswith(".csv")
                ]
                if downloaded_files:
                    # Rename the most recent file
                    latest_file = max(
                        [os.path.join(self.output_dir, f) for f in downloaded_files],
                        key=os.path.getctime,
                    )
                    if latest_file != expected_path:
                        os.rename(latest_file, expected_path)
                    self.logger.info(f"Successfully downloaded: {expected_path}")
                    return True
                else:
                    self.logger.warning(f"No CSV file found after download attempt")
                    return False

            except NoSuchElementException:
                self.logger.warning("Could not find CSV download button")
                return False

        except Exception as e:
            self.logger.error(f"Error downloading data for {station_name}: {e}")
            return False

    def scrape_past_years_selenium(self, years: int = 3) -> None:
        """Scrape data for the past N years using Selenium"""
        if not self.driver:
            self.logger.error("WebDriver not initialized")
            return

        end_date = datetime.now()
        start_date = end_date - timedelta(days=years * 365)

        self.logger.info(
            f"Scraping data from {start_date.strftime('%Y-%m')} to {end_date.strftime('%Y-%m')}"
        )

        # Get agricultural stations list
        stations = self.get_agricultural_stations()
        if not stations:
            self.logger.error("No agricultural stations found")
            return

        self.logger.info(f"Found {len(stations)} agricultural stations")

        # Download data for each station and each month
        current_date = start_date.replace(day=1)
        successful_downloads = 0
        total_attempts = 0

        while current_date <= end_date:
            year = current_date.year
            month = current_date.month

            self.logger.info(f"Processing {year}-{month:02d}")

            for station in stations:
                station_id = station.get("id") or station.get("stationId")
                station_name = station.get("name", "Unknown")

                if not station_id:
                    continue

                total_attempts += 1
                success = self.download_monthly_data_selenium(
                    station_id, station_name, year, month
                )

                if success:
                    successful_downloads += 1

                # Add delay to avoid overwhelming the server
                time.sleep(2)

            # Move to next month
            if month == 12:
                current_date = current_date.replace(year=year + 1, month=1)
            else:
                current_date = current_date.replace(month=month + 1)

        self.logger.info(
            f"Download completed: {successful_downloads}/{total_attempts} successful"
        )

    def alternative_scrape_method(self):
        """Alternative method using direct form submission"""
        try:
            # Get the main page to understand the form structure
            main_page = self.session.get(f"{self.base_url}/StationData")

            # Look for CSRF tokens or form data
            # This would need to be customized based on actual form structure

            # Example form data (adjust based on actual website)
            form_data = {
                "dataType": "monthly",
                "stationType": "agricultural",
                "startYear": datetime.now().year - 3,
                "endYear": datetime.now().year,
                "format": "csv",
            }

            # Submit form
            response = self.session.post(
                f"{self.base_url}/StationData/download", data=form_data
            )

            if response.status_code == 200:
                # Save the downloaded file
                filename = (
                    f"agricultural_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                )
                filepath = os.path.join(self.output_dir, filename)

                with open(filepath, "wb") as f:
                    f.write(response.content)

                self.logger.info(f"Downloaded data to {filepath}")
                return True

        except Exception as e:
            self.logger.error(f"Alternative scrape method failed: {e}")
            return False

    def manual_download_instructions(self):
        """Provide manual download instructions"""
        instructions = """
        Manual Download Instructions:
        1. Go to https://codis.cwa.gov.tw/StationData
        2. Select station type: Agricultural (農業站)
        3. Select data type: Monthly (月報表)
        4. Set time range: Past 3 years
        5. Choose format: CSV
        6. Download the files
        
        If the automated script doesn't work, use these manual steps.
        """

        print(instructions)

        # Save instructions to file
        with open(
            os.path.join(self.output_dir, "manual_instructions.txt"),
            "w",
            encoding="utf-8",
        ) as f:
            f.write(instructions)


def main():
    scraper = None
    try:
        scraper = CWAScraper()

        print("CWA Agricultural Station Data Scraper")
        print("=====================================")

        try:
            # Try the Selenium scraping method
            scraper.scrape_past_years_selenium(years=3)

        except Exception as e:
            print(f"Selenium scraping method failed: {e}")
            print("Trying alternative method...")

            # Try alternative method
            if not scraper.alternative_scrape_method():
                print("Alternative method also failed.")
                print("Providing manual download instructions...")
                scraper.manual_download_instructions()

        print(f"\nData saved to: {scraper.output_dir}")
        print("Check the output directory for downloaded files.")

    except Exception as e:
        print(f"Failed to initialize scraper: {e}")
        print("Please make sure ChromeDriver is installed and accessible.")

    finally:
        # Cleanup
        if scraper and scraper.driver:
            scraper.driver.quit()


if __name__ == "__main__":
    main()
