import requests
import pandas as pd
import os
from datetime import datetime, timedelta
import time
import logging
from typing import List, Dict, Optional
import json

class CWAScraper:
    def __init__(self):
        self.base_url = "https://codis.cwa.gov.tw"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Create output directory
        self.output_dir = "cwa_data"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def get_station_list(self) -> List[Dict]:
        """Get list of agricultural stations"""
        try:
            # This endpoint might need to be adjusted based on actual API
            stations_url = f"{self.base_url}/api/station"
            response = self.session.get(stations_url)
            
            if response.status_code == 200:
                stations_data = response.json()
                # Filter for agricultural stations
                agricultural_stations = [
                    station for station in stations_data 
                    if station.get('type') == 'agricultural' or 
                       '農業' in station.get('name', '')
                ]
                return agricultural_stations
            else:
                self.logger.error(f"Failed to get stations: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Error getting station list: {e}")
            return []
    
    def download_monthly_data(self, station_id: str, year: int, month: int) -> Optional[pd.DataFrame]:
        """Download monthly data for a specific station"""
        try:
            # Construct download URL (this may need adjustment based on actual API)
            download_url = f"{self.base_url}/StationData/MonthlyData"
            
            params = {
                'stationId': station_id,
                'year': year,
                'month': month,
                'format': 'csv'
            }
            
            response = self.session.get(download_url, params=params)
            
            if response.status_code == 200:
                # Try to parse as CSV
                try:
                    df = pd.read_csv(response.text)
                    return df
                except:
                    # If CSV parsing fails, save raw content
                    filename = f"{station_id}_{year}_{month:02d}.csv"
                    filepath = os.path.join(self.output_dir, filename)
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    self.logger.info(f"Saved raw data to {filepath}")
                    return None
            else:
                self.logger.warning(f"Failed to download data for {station_id} {year}-{month:02d}: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error downloading data for {station_id} {year}-{month:02d}: {e}")
            return None
    
    def scrape_past_years(self, years: int = 3) -> None:
        """Scrape data for the past N years"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=years * 365)
        
        self.logger.info(f"Scraping data from {start_date.strftime('%Y-%m')} to {end_date.strftime('%Y-%m')}")
        
        # Get station list
        stations = self.get_station_list()
        if not stations:
            self.logger.error("No agricultural stations found")
            return
        
        self.logger.info(f"Found {len(stations)} agricultural stations")
        
        # Download data for each station and each month
        current_date = start_date.replace(day=1)
        
        while current_date <= end_date:
            year = current_date.year
            month = current_date.month
            
            self.logger.info(f"Processing {year}-{month:02d}")
            
            for station in stations:
                station_id = station.get('id') or station.get('stationId')
                station_name = station.get('name', 'Unknown')
                
                self.logger.info(f"  Downloading data for {station_name} ({station_id})")
                
                df = self.download_monthly_data(station_id, year, month)
                
                if df is not None:
                    # Save to CSV
                    filename = f"{station_id}_{station_name}_{year}_{month:02d}.csv"
                    # Clean filename
                    filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
                    filepath = os.path.join(self.output_dir, filename)
                    
                    df.to_csv(filepath, index=False, encoding='utf-8-sig')
                    self.logger.info(f"    Saved to {filepath}")
                
                # Add delay to avoid overwhelming the server
                time.sleep(1)
            
            # Move to next month
            if month == 12:
                current_date = current_date.replace(year=year + 1, month=1)
            else:
                current_date = current_date.replace(month=month + 1)
    
    def alternative_scrape_method(self):
        """Alternative method using direct form submission"""
        try:
            # Get the main page to understand the form structure
            main_page = self.session.get(f"{self.base_url}/StationData")
            
            # Look for CSRF tokens or form data
            # This would need to be customized based on actual form structure
            
            # Example form data (adjust based on actual website)
            form_data = {
                'dataType': 'monthly',
                'stationType': 'agricultural',
                'startYear': datetime.now().year - 3,
                'endYear': datetime.now().year,
                'format': 'csv'
            }
            
            # Submit form
            response = self.session.post(f"{self.base_url}/StationData/download", data=form_data)
            
            if response.status_code == 200:
                # Save the downloaded file
                filename = f"agricultural_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                filepath = os.path.join(self.output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                self.logger.info(f"Downloaded data to {filepath}")
                return True
            
        except Exception as e:
            self.logger.error(f"Alternative scrape method failed: {e}")
            return False
    
    def manual_download_instructions(self):
        """Provide manual download instructions"""
        instructions = """
        Manual Download Instructions:
        1. Go to https://codis.cwa.gov.tw/StationData
        2. Select station type: Agricultural (農業站)
        3. Select data type: Monthly (月報表)
        4. Set time range: Past 3 years
        5. Choose format: CSV
        6. Download the files
        
        If the automated script doesn't work, use these manual steps.
        """
        
        print(instructions)
        
        # Save instructions to file
        with open(os.path.join(self.output_dir, "manual_instructions.txt"), 'w', encoding='utf-8') as f:
            f.write(instructions)


def main():
    scraper = CWAScraper()
    
    print("CWA Agricultural Station Data Scraper")
    print("=====================================")
    
    try:
        # Try the main scraping method
        scraper.scrape_past_years(years=3)
        
    except Exception as e:
        print(f"Main scraping method failed: {e}")
        print("Trying alternative method...")
        
        # Try alternative method
        if not scraper.alternative_scrape_method():
            print("Alternative method also failed.")
            print("Providing manual download instructions...")
            scraper.manual_download_instructions()
    
    print(f"\nData saved to: {scraper.output_dir}")
    print("Check the output directory for downloaded files.")


if __name__ == "__main__":
    main()