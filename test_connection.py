#!/usr/bin/env python3
"""
測試CODiS網站連接的腳本
"""

import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time


def test_requests_connection():
    """測試使用requests連接CODiS網站"""
    print("測試使用requests連接CODiS網站...")
    try:
        response = requests.get("https://codis.cwa.gov.tw/StationData", timeout=10)
        print(f"HTTP狀態碼: {response.status_code}")
        if response.status_code == 200:
            print("✓ requests連接成功")
            return True
        else:
            print("✗ requests連接失敗")
            return False
    except Exception as e:
        print(f"✗ requests連接錯誤: {e}")
        return False


def test_selenium_connection():
    """測試使用Selenium連接CODiS網站"""
    print("\n測試使用Selenium連接CODiS網站...")
    driver = None
    try:
        # 設置Chrome選項
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        # 初始化WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(10)
        
        # 訪問網站
        print("正在訪問CODiS網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        
        # 等待頁面載入
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 獲取頁面標題
        title = driver.title
        print(f"頁面標題: {title}")
        
        # 檢查是否包含預期內容
        if "CODiS" in title or "氣候觀測" in title:
            print("✓ Selenium連接成功")
            
            # 嘗試找到一些關鍵元素
            try:
                # 查找可能的農業站相關元素
                elements = driver.find_elements(By.XPATH, "//*[contains(text(), '農業')]")
                if elements:
                    print(f"✓ 找到 {len(elements)} 個包含'農業'的元素")
                else:
                    print("! 未找到包含'農業'的元素")
                
                # 查找可能的月報表相關元素
                elements = driver.find_elements(By.XPATH, "//*[contains(text(), '月報表')]")
                if elements:
                    print(f"✓ 找到 {len(elements)} 個包含'月報表'的元素")
                else:
                    print("! 未找到包含'月報表'的元素")
                
                # 查找可能的CSV相關元素
                elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'CSV')]")
                if elements:
                    print(f"✓ 找到 {len(elements)} 個包含'CSV'的元素")
                else:
                    print("! 未找到包含'CSV'的元素")
                    
            except Exception as e:
                print(f"查找元素時發生錯誤: {e}")
            
            return True
        else:
            print("✗ 頁面內容不符合預期")
            return False
            
    except Exception as e:
        print(f"✗ Selenium連接錯誤: {e}")
        return False
    finally:
        if driver:
            driver.quit()


def test_chromedriver():
    """測試ChromeDriver是否正常工作"""
    print("\n測試ChromeDriver...")
    try:
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        if "Google" in title:
            print("✓ ChromeDriver工作正常")
            return True
        else:
            print("✗ ChromeDriver測試失敗")
            return False
    except Exception as e:
        print(f"✗ ChromeDriver錯誤: {e}")
        return False


def main():
    """主函數"""
    print("CODiS網站連接測試")
    print("=" * 30)
    
    # 測試ChromeDriver
    chromedriver_ok = test_chromedriver()
    
    # 測試requests連接
    requests_ok = test_requests_connection()
    
    # 測試Selenium連接
    selenium_ok = test_selenium_connection()
    
    print("\n" + "=" * 30)
    print("測試結果摘要:")
    print(f"ChromeDriver: {'✓' if chromedriver_ok else '✗'}")
    print(f"Requests連接: {'✓' if requests_ok else '✗'}")
    print(f"Selenium連接: {'✓' if selenium_ok else '✗'}")
    
    if chromedriver_ok and selenium_ok:
        print("\n✓ 所有測試通過，可以運行爬蟲程式！")
        return True
    else:
        print("\n✗ 部分測試失敗，請檢查網路連接和ChromeDriver安裝")
        return False


if __name__ == "__main__":
    main()
