#!/usr/bin/env python3
"""
CODiS 中央氣象署農業站月報表資料爬蟲
簡化版本 - 專門針對CODiS網站結構設計
"""

import requests
import pandas as pd
import os
from datetime import datetime, timedelta
import time
import logging
from typing import List, Dict, Optional
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import re


class CODiSAgriculturalScraper:
    """CODiS 農業站資料爬蟲"""
    
    def __init__(self, headless=True):
        self.base_url = "https://codis.cwa.gov.tw"
        self.output_dir = "agricultural_data"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.output_dir, 'scraper.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Setup Chrome driver
        self.driver = None
        self.setup_driver(headless)
        
    def setup_driver(self, headless=True):
        """設置Chrome WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            # 設置下載目錄
            prefs = {
                "download.default_directory": os.path.abspath(self.output_dir),
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            self.logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            self.logger.error(f"Chrome driver 設置失敗: {e}")
            self.logger.info("請確保已安裝 ChromeDriver 並在 PATH 中")
            raise
            
    def __del__(self):
        """清理資源"""
        if hasattr(self, 'driver') and self.driver:
            self.driver.quit()
    
    def navigate_to_station_data(self):
        """導航到測站資料頁面"""
        try:
            self.logger.info("導航到CODiS測站資料頁面...")
            self.driver.get(f"{self.base_url}/StationData")
            
            # 等待頁面載入
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待JavaScript載入完成
            time.sleep(3)
            
            self.logger.info("頁面載入完成")
            return True
            
        except Exception as e:
            self.logger.error(f"導航失敗: {e}")
            return False
    
    def select_agricultural_stations(self):
        """選擇農業站"""
        try:
            self.logger.info("選擇農業站...")
            
            # 嘗試多種可能的農業站選擇方式
            selectors = [
                "//input[@type='checkbox' and contains(@value, '農業')]",
                "//input[@type='checkbox' and contains(@id, 'agricultural')]",
                "//label[contains(text(), '農業站')]//input",
                "//span[contains(text(), '農業站')]//input",
                "//div[contains(@class, 'station-type')]//input[contains(@value, '農業')]"
            ]
            
            for selector in selectors:
                try:
                    checkbox = self.driver.find_element(By.XPATH, selector)
                    if not checkbox.is_selected():
                        self.driver.execute_script("arguments[0].click();", checkbox)
                        time.sleep(1)
                        self.logger.info("農業站選項已選擇")
                        return True
                except NoSuchElementException:
                    continue
            
            self.logger.warning("未找到農業站選擇選項，繼續執行...")
            return True
            
        except Exception as e:
            self.logger.error(f"選擇農業站失敗: {e}")
            return False
    
    def select_monthly_report(self):
        """選擇月報表"""
        try:
            self.logger.info("選擇月報表...")
            
            # 嘗試多種可能的月報表選擇方式
            selectors = [
                "//input[@type='radio' and contains(@value, '月報表')]",
                "//label[contains(text(), '月報表')]//input",
                "//option[contains(text(), '月報表')]",
                "//div[contains(@class, 'report-type')]//input[contains(@value, '月報表')]"
            ]
            
            for selector in selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.tag_name == 'option':
                        # 如果是option，需要先找到select元素
                        select_element = element.find_element(By.XPATH, "./..")
                        select = Select(select_element)
                        select.select_by_visible_text('月報表')
                    else:
                        self.driver.execute_script("arguments[0].click();", element)
                    
                    time.sleep(1)
                    self.logger.info("月報表選項已選擇")
                    return True
                except NoSuchElementException:
                    continue
            
            self.logger.warning("未找到月報表選擇選項，繼續執行...")
            return True
            
        except Exception as e:
            self.logger.error(f"選擇月報表失敗: {e}")
            return False
    
    def set_date_range(self, start_year: int, start_month: int, end_year: int, end_month: int):
        """設置日期範圍"""
        try:
            self.logger.info(f"設置日期範圍: {start_year}/{start_month} - {end_year}/{end_month}")
            
            # 嘗試設置開始年月
            try:
                start_year_select = Select(self.driver.find_element(By.NAME, "startYear"))
                start_year_select.select_by_value(str(start_year))
                
                start_month_select = Select(self.driver.find_element(By.NAME, "startMonth"))
                start_month_select.select_by_value(str(start_month))
                
                end_year_select = Select(self.driver.find_element(By.NAME, "endYear"))
                end_year_select.select_by_value(str(end_year))
                
                end_month_select = Select(self.driver.find_element(By.NAME, "endMonth"))
                end_month_select.select_by_value(str(end_month))
                
                time.sleep(1)
                self.logger.info("日期範圍設置成功")
                return True
                
            except NoSuchElementException:
                # 嘗試其他日期輸入方式
                year_inputs = self.driver.find_elements(By.XPATH, "//input[@type='number' or @type='text']")
                for input_elem in year_inputs:
                    placeholder = input_elem.get_attribute('placeholder') or ''
                    if '年' in placeholder:
                        input_elem.clear()
                        input_elem.send_keys(str(start_year))
                    elif '月' in placeholder:
                        input_elem.clear()
                        input_elem.send_keys(str(start_month))
                
                self.logger.info("使用替代方式設置日期")
                return True
                
        except Exception as e:
            self.logger.error(f"設置日期範圍失敗: {e}")
            return False
    
    def download_csv_data(self):
        """下載CSV資料"""
        try:
            self.logger.info("嘗試下載CSV資料...")
            
            # 嘗試多種可能的CSV下載按鈕
            selectors = [
                "//button[contains(text(), 'CSV')]",
                "//a[contains(text(), 'CSV')]",
                "//input[@value='CSV']",
                "//button[contains(@class, 'csv')]",
                "//a[contains(@class, 'download')]",
                "//button[contains(text(), '下載')]"
            ]
            
            for selector in selectors:
                try:
                    download_button = self.driver.find_element(By.XPATH, selector)
                    self.driver.execute_script("arguments[0].click();", download_button)
                    time.sleep(5)  # 等待下載
                    
                    # 檢查是否有檔案下載
                    csv_files = [f for f in os.listdir(self.output_dir) if f.endswith('.csv')]
                    if csv_files:
                        self.logger.info(f"CSV下載成功，找到 {len(csv_files)} 個檔案")
                        return True
                        
                except NoSuchElementException:
                    continue
            
            self.logger.warning("未找到CSV下載按鈕")
            return False
            
        except Exception as e:
            self.logger.error(f"下載CSV失敗: {e}")
            return False
    
    def scrape_agricultural_data(self, years: int = 3):
        """爬取農業站資料"""
        try:
            # 計算日期範圍
            end_date = datetime.now()
            start_date = end_date - timedelta(days=years * 365)
            
            self.logger.info(f"開始爬取過去{years}年的農業站月報表資料")
            self.logger.info(f"日期範圍: {start_date.strftime('%Y/%m')} - {end_date.strftime('%Y/%m')}")
            
            # 導航到頁面
            if not self.navigate_to_station_data():
                return False
            
            # 選擇農業站
            if not self.select_agricultural_stations():
                return False
            
            # 選擇月報表
            if not self.select_monthly_report():
                return False
            
            # 設置日期範圍
            if not self.set_date_range(
                start_date.year, start_date.month,
                end_date.year, end_date.month
            ):
                return False
            
            # 下載資料
            if not self.download_csv_data():
                return False
            
            self.logger.info("資料爬取完成")
            return True
            
        except Exception as e:
            self.logger.error(f"爬取資料失敗: {e}")
            return False
    
    def manual_instructions(self):
        """提供手動下載說明"""
        instructions = """
        手動下載說明：
        1. 開啟 https://codis.cwa.gov.tw/StationData
        2. 在左側篩選條件中勾選「農業站」
        3. 選擇資料類型為「月報表(逐日資料)」
        4. 設定時間範圍為過去3年
        5. 點擊「CSV下載」按鈕
        6. 重複以上步驟直到下載所有農業站資料
        
        注意：如果自動化腳本無法正常運作，請使用此手動方式。
        """
        
        print(instructions)
        
        # 儲存說明到檔案
        with open(os.path.join(self.output_dir, "manual_instructions.txt"), 'w', encoding='utf-8') as f:
            f.write(instructions)


def main():
    """主函數"""
    scraper = None
    try:
        print("CODiS 農業站月報表資料爬蟲")
        print("=" * 40)
        
        # 詢問是否使用無頭模式
        headless_input = input("是否使用無頭模式運行？(y/n，預設y): ").strip().lower()
        headless = headless_input != 'n'
        
        scraper = CODiSAgriculturalScraper(headless=headless)
        
        # 開始爬取
        success = scraper.scrape_agricultural_data(years=3)
        
        if not success:
            print("自動化爬取失敗，提供手動下載說明...")
            scraper.manual_instructions()
        
        print(f"\n資料儲存位置: {scraper.output_dir}")
        print("請檢查輸出目錄中的下載檔案。")
        
    except Exception as e:
        print(f"程式執行失敗: {e}")
        print("請確保已安裝 ChromeDriver 並可正常存取。")
        
    finally:
        if scraper:
            del scraper


if __name__ == "__main__":
    main()
