#!/usr/bin/env python3
"""
自動安裝ChromeDriver的腳本
"""

import os
import sys
import requests
import zipfile
import platform
import subprocess
from pathlib import Path


def get_chrome_version():
    """獲取Chrome瀏覽器版本"""
    try:
        if platform.system() == "Windows":
            # Windows
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
            version, _ = winreg.QueryValueEx(key, "version")
            return version.split('.')[0]
        elif platform.system() == "Darwin":
            # macOS
            result = subprocess.run(["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "--version"], 
                                  capture_output=True, text=True)
            return result.stdout.split()[2].split('.')[0]
        else:
            # Linux
            result = subprocess.run(["google-chrome", "--version"], capture_output=True, text=True)
            return result.stdout.split()[2].split('.')[0]
    except Exception as e:
        print(f"無法獲取Chrome版本: {e}")
        return None


def get_chromedriver_version():
    """獲取最新的ChromeDriver版本"""
    try:
        chrome_version = get_chrome_version()
        if not chrome_version:
            # 如果無法獲取Chrome版本，使用最新版本
            response = requests.get("https://chromedriver.storage.googleapis.com/LATEST_RELEASE")
            return response.text.strip()
        
        # 根據Chrome版本獲取對應的ChromeDriver版本
        response = requests.get(f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{chrome_version}")
        if response.status_code == 200:
            return response.text.strip()
        else:
            # 如果找不到對應版本，使用最新版本
            response = requests.get("https://chromedriver.storage.googleapis.com/LATEST_RELEASE")
            return response.text.strip()
    except Exception as e:
        print(f"獲取ChromeDriver版本失敗: {e}")
        return None


def download_chromedriver(version):
    """下載ChromeDriver"""
    try:
        system = platform.system()
        if system == "Windows":
            filename = "chromedriver_win32.zip"
        elif system == "Darwin":
            filename = "chromedriver_mac64.zip"
        else:
            filename = "chromedriver_linux64.zip"
        
        url = f"https://chromedriver.storage.googleapis.com/{version}/{filename}"
        
        print(f"下載ChromeDriver {version}...")
        response = requests.get(url)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        return filename
    except Exception as e:
        print(f"下載ChromeDriver失敗: {e}")
        return None


def extract_chromedriver(zip_filename):
    """解壓縮ChromeDriver"""
    try:
        with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
            zip_ref.extractall('.')
        
        os.remove(zip_filename)
        
        # 在Windows上，確保檔案有執行權限
        if platform.system() != "Windows":
            os.chmod('chromedriver', 0o755)
        
        return True
    except Exception as e:
        print(f"解壓縮失敗: {e}")
        return False


def install_chromedriver():
    """安裝ChromeDriver"""
    print("開始安裝ChromeDriver...")
    
    # 檢查是否已經安裝
    try:
        result = subprocess.run(["chromedriver", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"ChromeDriver已安裝: {result.stdout.strip()}")
            return True
    except:
        pass
    
    # 獲取版本
    version = get_chromedriver_version()
    if not version:
        print("無法獲取ChromeDriver版本")
        return False
    
    print(f"將安裝ChromeDriver版本: {version}")
    
    # 下載
    zip_filename = download_chromedriver(version)
    if not zip_filename:
        return False
    
    # 解壓縮
    if not extract_chromedriver(zip_filename):
        return False
    
    # 移動到適當位置
    try:
        current_dir = Path.cwd()
        chromedriver_path = current_dir / ("chromedriver.exe" if platform.system() == "Windows" else "chromedriver")
        
        if chromedriver_path.exists():
            print(f"ChromeDriver已安裝到: {chromedriver_path}")
            
            # 將當前目錄添加到PATH（僅對當前會話有效）
            current_path = os.environ.get('PATH', '')
            if str(current_dir) not in current_path:
                os.environ['PATH'] = f"{current_dir}{os.pathsep}{current_path}"
            
            return True
        else:
            print("ChromeDriver安裝失敗")
            return False
            
    except Exception as e:
        print(f"移動ChromeDriver失敗: {e}")
        return False


def main():
    """主函數"""
    print("ChromeDriver自動安裝程式")
    print("=" * 30)
    
    if install_chromedriver():
        print("ChromeDriver安裝成功！")
        print("現在可以運行爬蟲程式了。")
    else:
        print("ChromeDriver安裝失敗！")
        print("請手動下載並安裝ChromeDriver:")
        print("1. 前往 https://chromedriver.chromium.org/")
        print("2. 下載與你的Chrome版本相符的ChromeDriver")
        print("3. 將chromedriver.exe放到專案目錄或PATH中")


if __name__ == "__main__":
    main()
