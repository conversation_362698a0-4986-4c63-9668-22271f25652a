# CODiS 農業站月報表資料爬蟲 - 使用說明

## 快速開始

### 1. 安裝依賴套件
```bash
pip install -r requirements.txt
```

### 2. 安裝ChromeDriver
```bash
python install_chromedriver.py
```

### 3. 測試連接
```bash
python test_connection.py
```

### 4. 運行爬蟲
```bash
python cwa_simple_scraper.py
```

## 檔案說明

| 檔案名稱 | 說明 |
|---------|------|
| `cwa_simple_scraper.py` | **主要爬蟲程式**（推薦使用） |
| `cwa_scraper.py` | 完整版爬蟲程式 |
| `install_chromedriver.py` | ChromeDriver自動安裝程式 |
| `test_connection.py` | 連接測試程式 |
| `requirements.txt` | Python套件依賴清單 |

## 使用步驟詳解

### 步驟1: 環境準備
確保你的系統已安裝：
- Python 3.7+
- Google Chrome瀏覽器

### 步驟2: 安裝Python套件
在專案目錄執行：
```bash
pip install requests pandas beautifulsoup4 lxml selenium
```

或使用requirements.txt：
```bash
pip install -r requirements.txt
```

### 步驟3: 安裝ChromeDriver
運行自動安裝程式：
```bash
python install_chromedriver.py
```

這個程式會：
- 自動檢測你的Chrome版本
- 下載對應的ChromeDriver
- 安裝到專案目錄

### 步驟4: 測試環境
運行測試程式確保一切正常：
```bash
python test_connection.py
```

應該看到類似輸出：
```
ChromeDriver: ✓
Requests連接: ✗ (這是正常的，SSL證書問題)
Selenium連接: ✓
✓ 所有測試通過，可以運行爬蟲程式！
```

### 步驟5: 運行爬蟲
```bash
python cwa_simple_scraper.py
```

程式會詢問是否使用無頭模式：
- 輸入 `y` 或直接按Enter：無頭模式（不顯示瀏覽器視窗）
- 輸入 `n`：有頭模式（顯示瀏覽器視窗，可以看到操作過程）

## 輸出結果

### 資料儲存位置
- 預設儲存在 `agricultural_data/` 目錄
- 包含CSV格式的月報表資料
- 日誌檔案：`scraper.log`

### 檔案命名格式
```
{站點ID}_{站點名稱}_{年份}_{月份}.csv
```

例如：`A001_台北農業站_2024_01.csv`

## 常見問題解決

### Q1: ChromeDriver版本不匹配
**錯誤訊息**：`This version of ChromeDriver only supports Chrome version XX`

**解決方法**：
```bash
python install_chromedriver.py
```
重新運行安裝程式會自動下載正確版本。

### Q2: 找不到ChromeDriver
**錯誤訊息**：`chromedriver: command not found`

**解決方法**：
1. 確保ChromeDriver在專案目錄中
2. 或將ChromeDriver路徑加入系統PATH

### Q3: 網站元素找不到
**錯誤訊息**：`NoSuchElementException`

**解決方法**：
1. 網站結構可能已變更
2. 嘗試使用有頭模式查看實際頁面
3. 檢查網站是否正常運作

### Q4: 下載失敗
**可能原因**：
- 網路連線問題
- 網站暫時無法存取
- 反爬蟲機制

**解決方法**：
1. 檢查網路連線
2. 稍後重試
3. 使用手動下載方式

## 手動下載方式

如果自動化爬蟲無法正常運作：

1. 開啟瀏覽器，前往：https://codis.cwa.gov.tw/StationData
2. 在左側篩選條件中勾選「農業站」
3. 選擇資料類型為「月報表(逐日資料)」
4. 設定時間範圍為過去3年
5. 點擊「CSV下載」按鈕
6. 重複以上步驟直到下載所有農業站資料

## 程式特色

### 簡化版爬蟲優勢
- 專門針對CODiS網站優化
- 多種元素選擇策略，適應網站變更
- 完整的錯誤處理和重試機制
- 詳細的日誌記錄
- 支援有頭/無頭模式

### 安全特性
- 遵守網站使用條款
- 合理的請求間隔，避免對伺服器造成負擔
- 自動重試機制，提高成功率

## 注意事項

1. **合法使用**：僅供研究和學習使用
2. **遵守規範**：請遵守CODiS網站使用條款
3. **合理頻率**：避免過於頻繁的請求
4. **資料引用**：使用資料時請註明來源

## 技術支援

如果遇到問題：
1. 首先運行 `python test_connection.py` 檢查環境
2. 查看 `scraper.log` 日誌檔案了解詳細錯誤
3. 嘗試使用有頭模式觀察實際操作過程
4. 如果問題持續，可以使用手動下載方式

## 更新維護

由於網站結構可能會變更，建議：
- 定期測試程式是否正常運作
- 關注CODiS網站的更新公告
- 必要時更新選擇器和邏輯
