# CODiS 中央氣象署農業站月報表資料爬蟲

這個專案提供了兩個Python腳本來爬取中央氣象署CODiS系統的農業站月報表資料。

## 檔案說明

- `cwa_scraper.py` - 完整版爬蟲，包含多種爬取方法
- `cwa_simple_scraper.py` - 簡化版爬蟲，專門針對CODiS網站設計
- `requirements.txt` - 所需的Python套件清單

## 安裝需求

### 1. Python環境
確保已安裝Python 3.7或更高版本。

### 2. 安裝Python套件
```bash
pip install -r requirements.txt
```

### 3. 安裝ChromeDriver
這個爬蟲使用Selenium和Chrome瀏覽器，需要安裝ChromeDriver：

#### Windows:
1. 下載ChromeDriver: https://chromedriver.chromium.org/
2. 將chromedriver.exe放到PATH環境變數中，或放到專案目錄
3. 或使用chocolatey: `choco install chromedriver`

#### macOS:
```bash
brew install chromedriver
```

#### Linux:
```bash
# Ubuntu/Debian
sudo apt-get install chromium-chromedriver

# 或手動下載並設置
wget https://chromedriver.storage.googleapis.com/LATEST_RELEASE
# 下載對應版本的ChromeDriver並放到/usr/local/bin/
```

## 使用方法

### 方法1: 使用簡化版爬蟲（推薦）
```bash
python cwa_simple_scraper.py
```

這個腳本會：
1. 自動開啟CODiS網站
2. 選擇農業站
3. 設定為月報表資料
4. 設定過去3年的時間範圍
5. 下載CSV檔案到`agricultural_data`目錄

### 方法2: 使用完整版爬蟲
```bash
python cwa_scraper.py
```

## 輸出結果

下載的資料會儲存在以下目錄：
- `agricultural_data/` (簡化版)
- `cwa_data/` (完整版)

檔案格式：
- CSV格式的月報表資料
- 檔名格式：`{站點ID}_{站點名稱}_{年份}_{月份}.csv`
- 日誌檔案：`scraper.log`

## 功能特色

### 簡化版爬蟲 (cwa_simple_scraper.py)
- 專門針對CODiS網站結構優化
- 支援無頭模式和有頭模式
- 自動重試機制
- 詳細的日誌記錄
- 提供手動下載說明

### 完整版爬蟲 (cwa_scraper.py)
- 多種爬取方法
- 支援API和Selenium兩種方式
- 更完整的錯誤處理
- 備用爬取方案

## 常見問題

### 1. ChromeDriver版本不匹配
確保ChromeDriver版本與你的Chrome瀏覽器版本相容。

### 2. 網站結構變更
如果CODiS網站結構發生變更，可能需要更新選擇器。

### 3. 下載失敗
- 檢查網路連線
- 確認CODiS網站是否正常運作
- 查看日誌檔案了解詳細錯誤訊息

### 4. 權限問題
確保有寫入輸出目錄的權限。

## 手動下載方式

如果自動化腳本無法正常運作，可以手動下載：

1. 開啟 https://codis.cwa.gov.tw/StationData
2. 在左側篩選條件中勾選「農業站」
3. 選擇資料類型為「月報表(逐日資料)」
4. 設定時間範圍為過去3年
5. 點擊「CSV下載」按鈕
6. 重複以上步驟直到下載所有農業站資料

## 注意事項

1. **遵守網站使用條款**：請確保遵守CODiS網站的使用條款和robots.txt規定
2. **合理使用**：避免過於頻繁的請求，以免對伺服器造成負擔
3. **資料使用**：下載的資料僅供研究和學習使用，商業用途請聯繫中央氣象署
4. **定期更新**：網站結構可能會變更，需要定期更新爬蟲程式

## 技術細節

- 使用Selenium WebDriver進行網頁自動化
- 支援多種元素選擇策略以適應網站變更
- 包含完整的錯誤處理和重試機制
- 自動檔案命名和組織

## 授權

此專案僅供教育和研究目的使用。使用時請遵守相關法律法規和網站使用條款。

## 聯絡資訊

如有問題或建議，請透過GitHub Issues回報。
